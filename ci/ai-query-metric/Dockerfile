FROM harbor-hkidc.kcprd.com/tmp/ai-base-python3.12:v0.1

ARG branchName
ARG appName
ARG secPwd
ARG gitUrl
ARG commitId
ARG originBranchName
ENV branchName=${branchName} \
    secPwd=${secPwd} \
    appName=${appName} \
    GIT_URL=${gitUrl} \
    ORIGIN_BRANCH=${originBranchName} \

    COMMIT_SHORT_SHA=${commitId} \
    LANG=en_US.UTF-8
WORKDIR /app
COPY . /app

#RUN ln -sf /opt/conda/bin/python /usr/bin/python && ln -sf /opt/conda/bin/python /usr/bin/python3
RUN chown -R 888:888 /tmp \
    && chown -R 888:888 /app \
#    && chown -R 888:888 /usr \
    && chmod -R 777 /tmp \
    && pip3 -V \
    && pip3 install -i https://nexus.kcprd.com/repository/mix-devops/simple/ -r /app/requirements.txt --trusted-host nexus.kcprd.com --timeout=200 --retries 5 --no-cache-dir\
    && chmod +x /app/run.sh \
    && export REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt


ENTRYPOINT ["/app/run.sh"]
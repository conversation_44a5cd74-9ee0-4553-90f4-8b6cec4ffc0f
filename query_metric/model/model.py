# --*-- conding:utf-8 --*--
# @Time : 2025/2/13
# <AUTHOR> Chris
import asyncio
import os
import time
import threading

from typing import Optional, Literal, List, Any, Callable, Dict
from pydantic import BaseModel, Field, ValidationError

from query_metric.common.log_util import logger
from query_metric.config import CFG

__all__ = (
    'calculate_model',
    'calculate_batch',
)


class TimeRange(BaseModel):
    """Represents a time range with optional start, end times, and a limit."""
    start_time: Optional[int] = Field(None, description="Start time in Unix timestamp format")
    end_time: Optional[int] = Field(None, description="End time in Unix timestamp format")
    limit: Optional[int] = Field(None, description="Maximum number of records to retrieve")

class BaseFieldRequest(BaseModel):
    """Base class for field requests containing common fields."""
    symbol: str = Field(..., description="Symbol identifier (e.g., BTCUSDT)")
    interval: Optional[str] = Field(None, description="Time interval (e.g., 1m, 5m, 1h)")
    market_type: Literal["spot", "future"] = Field("spot", description="Market type (either 'spot' or 'future')")
    time_range: Optional[TimeRange] = Field(None, description="Time range for the request")
    params: Optional[Dict[str, Any]] = Field(None, description="Additional parameters for the request")

class CalculateRequest(BaseFieldRequest):
    """Request for calculating a single indicator."""
    indicator: str = Field(..., description="Indicator to calculate (e.g., SMA, EMA)")

class CalculateBatchRequest(BaseFieldRequest):
    """Request for calculating multiple indicators."""
    indicators: List[str] = Field(..., description="List of indicators to calculate (e.g., ['SMA', 'EMA'])")


# 统一数据校验映射表
VALIDATION_MAP = {
    "calculate": CalculateRequest,
    "calculate_batch": CalculateBatchRequest,
}


class ModelLoadError(Exception):
    """模型加载异常"""
    pass


class CustomBaseModel:
    def __init__(self):
        self.model = None

    def reload_model(self):
        """

        :return:
        """
        os.environ['spot_api_base'] = CFG.spot_api_base
        os.environ['futures_api_base'] = CFG.futures_api_base
        os.environ['spot_candles_path'] = CFG.spot_candles_path
        os.environ['spot_stats_path'] = CFG.spot_stats_path
        os.environ['futures_candles_path'] = CFG.futures_candles_path
        os.environ['futures_stats_path'] = CFG.futures_stats_path

        from query_metric.model.metric_calc.metric_calculator import MetricCalculator

        model = MetricCalculator()
        logger.info("模型创建成功")
        self.model =  model

    def validate(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """根据子类类型自动选择验证模型"""
        start_time = time.time()
        VALIDATION_MAP_TEMP = {key.replace('_', ''): value for key, value in VALIDATION_MAP.items()}
        field_class = VALIDATION_MAP_TEMP.get(self.__class__.__name__.lower().replace("custom", "").replace("model", ""))
        try:
            logger.info(f'validate data successful, used time: {(time.time() - start_time) * 1000}ms')
            return field_class(**data).model_dump()
        except ValidationError as ve:
            logger.error(f"Validation failed: {ve.json()}")
            raise ValueError(f"Invalid input data: {ve}")



# 定义具体模型实例，指定调用的方法名
class CustomCalculateModel(CustomBaseModel):
    async def predict_calculate(self, data):
        """
        :param data:
        :return:
        """
        start_time = time.time()
        data = self.validate(data)
        if not self.model:
            self.reload_model()
        outputs = await asyncio.to_thread(lambda: self.model.calculate(**data))
        logger.info(f'calculate model predict successful, used time: {(time.time() - start_time) * 1000}ms')
        return outputs


class CustomCalculateBatchModel(CustomBaseModel):
    async def predict_calculate_batch(self, data):
        """
        :param data:
        :return:
        """
        start_time = time.time()
        data = self.validate(data)
        if not self.model:
            self.reload_model()
        outputs = await asyncio.to_thread(lambda: self.model.calculate_batch(**data))
        logger.info(f'calculate batch model predict successful, used time: {(time.time() - start_time) * 1000}ms')
        return outputs


# 实例化模型
calculate_model = CustomCalculateModel()
calculate_batch = CustomCalculateBatchModel()

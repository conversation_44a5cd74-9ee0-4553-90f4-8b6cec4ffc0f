# --*-- conding:utf-8 --*--
# @Time : 2024/12/24
# <AUTHOR> <PERSON>
import time
import traceback

from fastapi import Request
from typing import Callable

from query_metric.base.fastapi_util import BaseHTTPView
from query_metric.router import index as router
from query_metric.common.log_util import logger
from query_metric.model import calculate_model, calculate_batch

# __all__ = (
#     'CCalculateDataView',
#     'CCalculateBatchDataView',
# )


class BaseModelView(BaseHTTPView):
    async def execute(self, request: Request, predict_func: Callable):
        start_time = time.time()
        req_data = await self.get_json(request)

        try:
            outputs = await predict_func(req_data)
        except Exception as e:
            logger.error(f'{predict_func.__name__} error: {traceback.format_exc()}')
            logger.error(f'inputs: {req_data}')
            raise e

        logger.info(f'{predict_func.__name__} used time: {(time.time() - start_time) * 1000}ms')
        return self.Jsonify.success(data=outputs)


class CCalculateDataView(BaseModelView):
    async def post(self, request: Request):
        return await self.execute(request, calculate_model.predict_calculate)

class CCalculateBatchDataView(BaseModelView):
    async def post(self, request: Request):
        return await self.execute(request, calculate_batch.predict_calculate_batch)

ENDPOINTS = [
    ("/calculate", CCalculateDataView().post),
    ("/calculate_batch", CCalculateBatchDataView().post),
]

for path, handler in ENDPOINTS:
    router.add_api_route(path=path, endpoint=handler, methods=["POST"])
